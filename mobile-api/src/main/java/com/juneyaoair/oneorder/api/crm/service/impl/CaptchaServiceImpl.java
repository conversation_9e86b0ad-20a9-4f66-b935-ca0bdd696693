package com.juneyaoair.oneorder.api.crm.service.impl;

import com.alibaba.fastjson.JSON;
import com.juneyaoair.flightbasic.utils.MdcUtils;
import com.juneyaoair.message.bean.message.MessageResult;
import com.juneyaoair.message.bean.message.SmsUserInfo;
import com.juneyaoair.mobile.exception.MultiLangServiceException;
import com.juneyaoair.mobile.exception.ServiceException;
import com.juneyaoair.oneorder.api.crm.config.CrmConfig;
import com.juneyaoair.oneorder.api.crm.constant.CrmUrlConstant;
import com.juneyaoair.oneorder.api.crm.service.ICaptchaService;
import com.juneyaoair.oneorder.crm.dto.PtApiCRMRequest;
import com.juneyaoair.oneorder.crm.dto.PtCRMResponse;
import com.juneyaoair.oneorder.crm.dto.login.PtLoginResponse;
import com.juneyaoair.oneorder.crm.dto.login.PtSendCaptchaRequest;
import com.juneyaoair.oneorder.tools.utils.HoAirGsonUtil;
import com.juneyaoair.oneorder.tools.utils.HttpResult;
import com.juneyaoair.oneorder.tools.utils.HttpUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description
 * @date 2018/8/9  19:36.
 */
@Slf4j
@Service
public class CaptchaServiceImpl implements ICaptchaService {
    @Autowired
    private CrmConfig crmConfig;

    @Autowired
    private MessageClientImpl messageClient;

    @Override
    public PtCRMResponse send(PtApiCRMRequest<PtSendCaptchaRequest> ptApiCRMRequest) {
        String url = crmConfig.getCrmLoginUrl() + CrmUrlConstant.CAPTCHA_SEND;
        HttpResult httpResult = HttpUtil.doPostClient(ptApiCRMRequest,url);
        String result = httpResult.getResponse();
        if(!httpResult.isResult()){
            throw MultiLangServiceException.fail("请求远程服务异常");
        }
        if (StringUtils.isBlank(result)) {
            throw new ServiceException("CRM请求异常:" + ptApiCRMRequest.getRequestId());
        }
        PtCRMResponse<PtLoginResponse> ptCRMResponse = HoAirGsonUtil.fromJson(result, PtCRMResponse.class);
        if (ptCRMResponse == null) {
            throw new ServiceException("数据转换异常:" + ptApiCRMRequest.getRequestId());
        }
        if (0 != ptCRMResponse.getCode()) {
            throw ServiceException.fail(ptCRMResponse.getMsg());
        }
        return ptCRMResponse;
    }

    @Override
    public boolean commonSmsSend(String templateCode, String areaId, String phone, Map<String, String> extras) {
        if (StringUtils.isAnyBlank(templateCode, phone) || null == extras || extras.isEmpty()) {
            log.info("请求ID：{} 手机号码:{}, 模板ID：{} 可变量：{} 短信发送参数校验不通过", MdcUtils.getRequestId(), phone, templateCode, null == extras ? "" : JSON.toJSONString(extras));
            return false;
        }
        try {
            // 由于短信平台不支持areaId 需将areaId与phone 拼接后使用
            areaId = null == areaId ? "" : areaId;
            List<MessageResult> messageResultList = messageClient.batchSmsMessage(templateCode, null, SmsUserInfo.getSmsUserInfoList(areaId + phone, extras), MdcUtils.getRequestId());
            if (CollectionUtils.isEmpty(messageResultList)) {
                return false;
            }
            MessageResult messageResult = messageResultList.get(0);
            return null != messageResult && StringUtils.isNotBlank(messageResult.getSendMessageId());
        } catch (Exception e) {
            log.info("请求ID：{} 手机号码:{}, 模板ID：{} 可变量：{} 短信发送失败", MdcUtils.getRequestId(), phone, templateCode, JSON.toJSONString(extras));
            return false;
        }
    }
}
