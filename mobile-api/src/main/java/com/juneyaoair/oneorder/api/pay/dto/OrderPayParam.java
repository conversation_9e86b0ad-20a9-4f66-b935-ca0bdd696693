package com.juneyaoair.oneorder.api.pay.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @description
 * @date 2023/7/4 10:10
 */
@Data
@NoArgsConstructor
@ApiModel(value = "OrderPayParam",description = "订单支付参数")
public class OrderPayParam extends InitPayMethodParam {
    @ApiModelProperty(value = "渠道订单编号")
    @NotBlank(message="渠道订单编号不能为空")
    private String channelOrderNo;
    @ApiModelProperty(value = "订单编号")
    @NotBlank(message="订单编号不能为空")
    private String orderNo;
    @ApiModelProperty(value = "渠道订单生成时间",notes = "格式：yyyyMMddHHmmss")
    @NotBlank(message="渠道订单生成时间不能为空")
    private String channelBuyDatetime;
    @ApiModelProperty(value = "支付金额",notes = "一千元表示为：1000.00")
    @NotBlank(message="支付金额不能为空")
    private String amount;
    @ApiModelProperty(value = "积分数")
    private String useScore;
    @ApiModelProperty(value = "渠道用户私有域",notes = "渠道用户自定义的字段、交易完成由支付平台原样返回")
    private String channelPriInfo;
    @NotBlank(message="支付方式不能为空")
    @ApiModelProperty(value = "支付方式")
    private String method;
    @ApiModelProperty(value = "信用卡信息")
    private CardInfo cardInfo;
    @ApiModelProperty(value = "前端回调地址")
    private String returnUrl;
}
