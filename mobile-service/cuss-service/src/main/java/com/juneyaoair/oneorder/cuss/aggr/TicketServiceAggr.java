package com.juneyaoair.oneorder.cuss.aggr;

import com.juneyaoair.flightbasic.common.BaseRequestDTO;
import com.juneyaoair.flightbasic.common.BaseResultDTO;
import com.juneyaoair.flightbasic.response.api.ApiAirPortInfoDto;
import com.juneyaoair.i18n.enums.I18nDictionaryTypeEnum;
import com.juneyaoair.mobile.exception.MultiLangServiceException;
import com.juneyaoair.mobile.exception.errorcode.CommonErrorCode;
import com.juneyaoair.oneorder.api.basic.service.I18nDictService;
import com.juneyaoair.oneorder.api.common.CacheService;
import com.juneyaoair.oneorder.api.common.CommonService;
import com.juneyaoair.oneorder.api.crm.service.IMemberService;
import com.juneyaoair.oneorder.api.cuss.dto.DetrMemberInfo;
import com.juneyaoair.oneorder.api.cuss.dto.TicketVerifyResponse;
import com.juneyaoair.oneorder.api.cuss.service.IVerifyTicketService;
import com.juneyaoair.oneorder.api.order.dto.ticket.QueryTicketRuleInfoResult;
import com.juneyaoair.oneorder.api.order.service.IOrderService;
import com.juneyaoair.oneorder.common.common.UnifiedOrderResultEnum;
import com.juneyaoair.oneorder.common.dto.BizDto;
import com.juneyaoair.oneorder.common.dto.enums.EticketStatusEnum;
import com.juneyaoair.oneorder.common.dto.enums.InterFlagEnum;
import com.juneyaoair.oneorder.common.enums.LanguageEnum;
import com.juneyaoair.oneorder.constant.PatternCommon;
import com.juneyaoair.oneorder.cuss.TaxInfo;
import com.juneyaoair.oneorder.cuss.bean.param.GeeTestTicketVerifyParam;
import com.juneyaoair.oneorder.mobile.config.AppConfig;
import com.juneyaoair.oneorder.mobile.dto.ChannelInfo;
import com.juneyaoair.oneorder.ticket.DetrSegmentDetail;
import com.juneyaoair.oneorder.ticket.FlightItineraryInfo;
import com.juneyaoair.oneorder.ticket.TicketFlight;
import com.juneyaoair.oneorder.ticket.TicketVerifyResultDto;
import com.juneyaoair.oneorder.tools.utils.DateUtil;
import com.juneyaoair.oneorder.util.BffI18nUtils;
import com.juneyaoair.oneorder.util.FlightUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.DecimalFormat;
import java.util.*;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @description
 * @date 2025/4/9 13:20
 */
@Service
public class TicketServiceAggr {
    @Autowired
    private IMemberService memberService;
    @Autowired
    private IVerifyTicketService verifyTicketService;
    @Autowired
    private CacheService cacheService;
    @Autowired
    private I18nDictService i18nDictService;
    @Autowired
    private IOrderService orderService;
    @Autowired
    private CommonService commonService;
    @Autowired
    private AppConfig appConfig;

    // 在类中添加 DecimalFormat 实例
    private static final DecimalFormat DECIMAL_FORMAT = new DecimalFormat("#.##");

    public TicketServiceAggr(IOrderService orderService) {
        this.orderService = orderService;
    }

    public TicketVerifyResultDto verifyTicketV2(String ffpId, String ffpNo, GeeTestTicketVerifyParam geeVerifyParam, BizDto bizDto, LanguageEnum language) {
        //账户实名判断
        if (!memberService.checkRealState(bizDto, ffpNo)) {
            throw MultiLangServiceException.fail(CommonErrorCode.NO_REAL_NAME);
        }
        DetrMemberInfo detrMemInfo = new DetrMemberInfo();
        detrMemInfo.setPassengerNm(geeVerifyParam.getPassengerName());
        detrMemInfo.setCertNo(geeVerifyParam.getTicketNo());
        detrMemInfo.setChannelNo(bizDto.getHeadChannelCode());
        detrMemInfo.setAnalyzeTicket(false);
        BaseRequestDTO<DetrMemberInfo> reqDTO = new BaseRequestDTO<>();
        reqDTO.setFfpId(ffpId);
        reqDTO.setFfpCardNo(ffpNo);
        reqDTO.setIp(bizDto.getIp());
        reqDTO.setChannelCode(bizDto.getHeadChannelCode());
        reqDTO.setVersion("30");
        reqDTO.setRequest(detrMemInfo);
        BaseResultDTO<TicketVerifyResponse> result = verifyTicketService.verifyTicket(reqDTO);
        if (ObjectUtils.isEmpty(result)) {
            throw MultiLangServiceException.fail(CommonErrorCode.SEAT_TICKET_INFO_FAIL);
        }
        if (!UnifiedOrderResultEnum.SUCCESS.getResultCode().equals(result.getResultCode())) {
            throw MultiLangServiceException.fail(CommonErrorCode.SEAT_TICKET_INFO_FAIL);
        }
        //航班信息匹配检验
        FlightItineraryInfo flightItineraryInfo = result.getResult().getData();
        if (StringUtils.isNotBlank(flightItineraryInfo.getSigningInfo())) {
            Pattern pattern = Pattern.compile(PatternCommon.GV_GROUP);
            if (pattern.matcher(flightItineraryInfo.getSigningInfo()).find()) {
                throw MultiLangServiceException.fail(CommonErrorCode.B2B_TIPS);
            }
        }
        if (StringUtils.isNotBlank(flightItineraryInfo.getTourCode()) && flightItineraryInfo.getTourCode().contains("B2BGROUP")) {
            throw MultiLangServiceException.fail(CommonErrorCode.B2B_TIPS);
        }
//        boolean segmentMatchFlag = flightItineraryInfo.detrSegmentDetailList.stream().anyMatch(
//                detrSegmentDetail ->
//                        geeVerifyParam.getFlightNo().equalsIgnoreCase(detrSegmentDetail.getFlightNo())
//                                && geeVerifyParam.getFlightDate().equalsIgnoreCase(detrSegmentDetail.getDepTime().substring(0, 10))
//        );
//        if (!segmentMatchFlag) {
//            throw MultiLangServiceException.fail(CommonErrorCode.SEAR_TRAVELLER_NO_TRIP);
//        }
        TicketVerifyResultDto ticketVerifyResultDto = new TicketVerifyResultDto();
        ticketVerifyResultDto.setTicketNo(flightItineraryInfo.getTicketNo());
        if (!flightItineraryInfo.isIT()) {
            ticketVerifyResultDto.setTotalCurrency(flightItineraryInfo.getCurrencyTypeTotal());
            ticketVerifyResultDto.setTotal(DECIMAL_FORMAT.format(flightItineraryInfo.getTotalAmount()));
            ticketVerifyResultDto.setFare(DECIMAL_FORMAT.format(flightItineraryInfo.getFare()));
            ticketVerifyResultDto.setTaxCurrency(taxCurrencyType(flightItineraryInfo.getTaxInfoList()));
            double yqcn = sumYQCNTax(flightItineraryInfo.getTaxInfoList());
            ticketVerifyResultDto.setYqcn(DECIMAL_FORMAT.format(yqcn));
            ticketVerifyResultDto.setOtherTax(DECIMAL_FORMAT.format(sumOtherTax(flightItineraryInfo.getTaxInfoList())));
        }
        if (CollectionUtils.isNotEmpty(flightItineraryInfo.getDetrSegmentDetailList())) {
            //去除缺口程航段
            flightItineraryInfo.getDetrSegmentDetailList().removeIf(detrSegmentDetail -> "VOID".equalsIgnoreCase(detrSegmentDetail.getTicketStatus()) || StringUtils.isBlank(detrSegmentDetail.getTicketStatus()));
            Date now = new Date();
            boolean timeLimit = flightItineraryInfo.getDetrSegmentDetailList().stream().anyMatch(segmentInfo -> {
                Date depDate = DateUtil.toDate(segmentInfo.getDepTime(), DateUtil.YYYY_MM_DD_HH_MM_PATTERN);
                //Date bookingDate = DateUtil.toDate(flightItineraryInfo.getIssueDate());
                if (EticketStatusEnum.OPEN_FOR_USE.getCode().equals(segmentInfo.getTicketStatus())
                        || EticketStatusEnum.CHECKED_IN.getCode().equals(segmentInfo.getTicketStatus())
                        || EticketStatusEnum.LIFT_BOARDED.getCode().equals(segmentInfo.getTicketStatus())) {
                    if (DateUtil.durDays(depDate, now) <= 365) {
                        return true;
                    }
                } else {
                    if (DateUtil.durDays(depDate, now) <= 90) {
                        return true;
                    }
                }
                return false;
            });
            if (!timeLimit) {
                throw MultiLangServiceException.fail(CommonErrorCode.FAIL, "未查询到客票，请确认输入信息是否有误或超过查询日期范围");
            }
            //国内国际标识
            boolean interFlag = flightItineraryInfo.getDetrSegmentDetailList().stream().anyMatch(detrSegmentDetail -> {
                ApiAirPortInfoDto depAirport = cacheService.getLocalAirport(detrSegmentDetail.getDepAirportCode());
                ApiAirPortInfoDto arrAirport = cacheService.getLocalAirport(detrSegmentDetail.getArrAirportCode());
                if (InterFlagEnum.I.getCode().equals(depAirport.getIsInternational()) || InterFlagEnum.I.getCode().equals(arrAirport.getIsInternational())) {
                    return true;
                } else {
                    return false;
                }
            });
            ticketVerifyResultDto.setInterFlag(interFlag);
            //返回航段结果处理
            List<TicketFlight> ticketFlightList = new ArrayList<>();
            Map<String, Map<String, String>> cabinMap = i18nDictService.fetchDictData(I18nDictionaryTypeEnum.CABIN_NAME);
            //退改规则查询
            ChannelInfo channelInfo = commonService.findChannelInfo(bizDto.getHeadChannelCode());
            QueryTicketRuleInfoResult queryTicketRuleInfoResult = orderService.queryTicketRuleInfo(channelInfo, geeVerifyParam.getTicketNo());
            for (DetrSegmentDetail detrSegmentDetail : flightItineraryInfo.getDetrSegmentDetailList()) {
                //去除缺口程
                if ("VOID".equalsIgnoreCase(detrSegmentDetail.getTicketStatus()) || StringUtils.isBlank(detrSegmentDetail.getTicketStatus())) {
                    continue;
                }
                TicketFlight ticketFlight = new TicketFlight();
                ticketFlight.setFlightNo(detrSegmentDetail.getFlightNo());
                if (StringUtils.isNotBlank(detrSegmentDetail.getDepTime())) {
                    ticketFlight.setFlightDate(detrSegmentDetail.getDepTime().substring(0, 10));
                }
                ticketFlight.setWeek(DateUtil.getWeekStrNoZhou(DateUtil.toDate(ticketFlight.getFlightDate(), DateUtil.YYYY_MM_DD_PATTERN)));
                ticketFlight.setCabin(detrSegmentDetail.getCabin());
                String cabinClass = appConfig.transferCabinClass(detrSegmentDetail.getCabin());
                ticketFlight.setCabinName(BffI18nUtils.getTranslation(cabinMap, cabinClass, language));
                if (StringUtils.isNotBlank(detrSegmentDetail.getDepTime())) {
                    if (detrSegmentDetail.getDepTime().length() > 16) {
                        ticketFlight.setDepTime(detrSegmentDetail.getDepTime().substring(0, 16));
                    } else {
                        ticketFlight.setDepTime(detrSegmentDetail.getDepTime());
                    }
                }
                if (StringUtils.isNotBlank(detrSegmentDetail.getArrTime())) {
                    if (detrSegmentDetail.getArrTime().length() > 16) {
                        ticketFlight.setArrTime(detrSegmentDetail.getArrTime().substring(0, 16));
                    } else {
                        ticketFlight.setArrTime(detrSegmentDetail.getArrTime());
                    }
                }
                ApiAirPortInfoDto depAirPortInfo = cacheService.getLocalAirport(detrSegmentDetail.getDepAirportCode());
                ticketFlight.setDepAirportName(BffI18nUtils.getAirportName(depAirPortInfo, language.name()));
                ticketFlight.setDepAirportTerminal(detrSegmentDetail.getDepAirportTerminal());
                ApiAirPortInfoDto arrAirPortInfo = cacheService.getLocalAirport(detrSegmentDetail.getArrAirportCode());
                ticketFlight.setArrAirportName(BffI18nUtils.getAirportName(arrAirPortInfo, language.name()));
                ticketFlight.setArrAirportTerminal(detrSegmentDetail.getArrAirportTerminal());
                ticketFlight.setTicketStatus(EticketStatusEnum.getEnumByCode(detrSegmentDetail.getTicketStatus()).getDesc());
                if (CollectionUtils.isNotEmpty(queryTicketRuleInfoResult.getSegRuleInfoList())) {
                    Optional<QueryTicketRuleInfoResult.SegRuleInfo> optional = queryTicketRuleInfoResult.getSegRuleInfoList().stream().filter(segRuleInfo ->
                            segRuleInfo.getDepAirport().equals(detrSegmentDetail.getDepAirportCode())
                                    && segRuleInfo.getArrAirport().equals(detrSegmentDetail.getArrAirportCode())).findFirst();
                    if (optional.isPresent()) {
                        QueryTicketRuleInfoResult.SegRuleInfo segRuleInfo = optional.get();
                        if (interFlag || flightItineraryInfo.isIT()) {
                            ticketFlight.setRefundRuleDesc(segRuleInfo.getRefundedComment());
                            ticketFlight.setChangeRuleDesc(segRuleInfo.getChangedComment());
                        } else {
                            ticketFlight.setRefundRule(FlightUtil.toRefundRules(segRuleInfo.getRefundedRules(), InterFlagEnum.D.getCode()));
                            ticketFlight.setChangeRule(FlightUtil.toChangeRules(segRuleInfo.getChangeRules(), InterFlagEnum.D.getCode()));
                        }
                    }
                }
                ticketFlightList.add(ticketFlight);
            }
            ticketVerifyResultDto.setFlightList(ticketFlightList);
        }
        return ticketVerifyResultDto;
    }


    /**
     * 计算税种为YQ或CN，且货币类型不为PD的税金总额
     *
     * @param taxInfoList 税收信息列表
     * @return 税金总额
     */
    private double sumYQCNTax(List<TaxInfo> taxInfoList) {
        // 检查列表是否为空，为空则返回0
        if (CollectionUtils.isEmpty(taxInfoList)) {
            return 0d;
        }
        // 使用流处理过滤出税种为YQ或CN，且货币类型不为PD的税收信息，并计算其税金总额
        return taxInfoList.stream()
                .filter(taxInfo -> ("yq".equalsIgnoreCase(taxInfo.getTaxCode()) || "cn".equalsIgnoreCase(taxInfo.getTaxCode())) && !"PD".equalsIgnoreCase(taxInfo.getTaxCurrencyType().trim()))
                .mapToDouble(TaxInfo::getTaxAmount)
                .sum();
    }

    /**
     * 计算特定条件下的其他税收总额
     * 本方法旨在计算给定税收信息列表中，排除了特定税收代码和货币类型的税收金额总和
     *
     * @param taxInfoList 税收信息列表，用于计算总和
     * @return 返回满足条件的税收金额总和
     */
    private double sumOtherTax(List<TaxInfo> taxInfoList) {
        // 检查列表是否为空，为空则返回0
        if (CollectionUtils.isEmpty(taxInfoList)) {
            return 0d;
        }
        // 使用流处理过滤并计算税收金额总和
        // 过滤条件为税收代码不是"yq"或"cn"，且货币类型不是"PD"
        return taxInfoList.stream()
                .filter(taxInfo -> !("yq".equalsIgnoreCase(taxInfo.getTaxCode()) || "cn".equalsIgnoreCase(taxInfo.getTaxCode())) && !"PD".equalsIgnoreCase(taxInfo.getTaxCurrencyType().trim()))
                .mapToDouble(TaxInfo::getTaxAmount)
                .sum();
    }

    /**
     * 获取税收信息列表中的第一个非“PD”类型的货币类型
     * 此方法用于处理税收信息列表，旨在找出第一个货币类型不是“PD”的税收信息
     * 如果列表为空或者所有税收信息的货币类型都是“PD”，则返回空字符串
     *
     * @param taxInfoList 税收信息列表，用于查找符合条件的货币类型
     * @return 返回第一个非“PD”类型的货币类型，如果不存在或列表为空，则返回空字符串
     */
    private String taxCurrencyType(List<TaxInfo> taxInfoList) {
        // 检查列表是否为空，为空则返回0
        if (CollectionUtils.isEmpty(taxInfoList)) {
            return "";
        }
        // 使用流处理过滤出第一个货币类型不是“PD”的税收信息
        Optional<TaxInfo> optional = taxInfoList.stream().filter(taxInfo -> !"PD".equals(taxInfo.getTaxCurrencyType())).findFirst();
        // 如果找到了符合条件的税收信息，则返回其货币类型
        if (optional.isPresent()) {
            return optional.get().getTaxCurrencyType();
        }
        // 如果没有找到符合条件的税收信息，则返回空字符串
        return "";
    }
}
