package com.juneyaoair.oneorder.cuss.bean.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;

/**
 * <AUTHOR>
 * @Description
 * @created 2023/12/11 9:52
 */
@Data
public class QueryTravellerTrip {

    @ApiModelProperty(value = "证件号（基于证件号查询必须）")
    public String travellerNumber;

    @ApiModelProperty(value = "旅客姓名（基于证件号查询必须）")
    public String travellerName;

    private String flightDate;

    @ApiModelProperty(value="航班号（基于证件号查询必须）")
    private String flightNo;

    @NotBlank(message = "查询类型不能为空")
    @Pattern(regexp = "member|number" ,message = "查询类型不正确")
    @ApiModelProperty(value = "查询类型 member：基于会员查询 number:基于证件号查询")
    private String type;

}
