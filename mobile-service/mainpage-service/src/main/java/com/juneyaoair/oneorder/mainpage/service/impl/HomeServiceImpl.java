package com.juneyaoair.oneorder.mainpage.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.MD5;
import com.fasterxml.jackson.core.type.TypeReference;
import com.github.pagehelper.PageInfo;
import com.juneyaoair.flightbasic.advertisement.AdvertisementDto;
import com.juneyaoair.flightbasic.advertisement.AdvertisementParam;
import com.juneyaoair.flightbasic.api.index.GetWechatQRCodeReq;
import com.juneyaoair.flightbasic.api.index.GetWechatQRCodeResp;
import com.juneyaoair.flightbasic.api.index.cobrandcreditcard.CoBrandCreditCardAdBO;
import com.juneyaoair.flightbasic.common.BaseRequestDTO;
import com.juneyaoair.flightbasic.common.BaseResponseDTO;
import com.juneyaoair.flightbasic.common.BaseResultDTO;
import com.juneyaoair.flightbasic.common.WSEnum;
import com.juneyaoair.flightbasic.commondto.RequestData;
import com.juneyaoair.flightbasic.commondto.ResponseData;
import com.juneyaoair.flightbasic.feign.FlightBasicConsumerClient;
import com.juneyaoair.flightbasic.feign.FlightBasicProviderClient;
import com.juneyaoair.flightbasic.mall.CommonRecommendTravel;
import com.juneyaoair.flightbasic.request.wechat.PicDTO;
import com.juneyaoair.flightbasic.request.wechat.PictureRequestDTO;
import com.juneyaoair.flightbasic.response.airport.AirPortInfoDTO;
import com.juneyaoair.flightbasic.response.api.ApiCityInfoDto;
import com.juneyaoair.flightbasic.response.api.CityWarnInfoDTO;
import com.juneyaoair.flightbasic.travelreminder.TravelReminderContentDTO;
import com.juneyaoair.flightbasic.travelreminder.TravelReminderResponse;
import com.juneyaoair.flightbasic.version.request.VersionReq;
import com.juneyaoair.flightbasic.version.response.VersionDto;
import com.juneyaoair.mobile.exception.ServiceException;
import com.juneyaoair.mobile.exception.errorcode.CommonErrorCode;
import com.juneyaoair.oneorder.api.basic.service.IBasicService;
import com.juneyaoair.oneorder.api.common.CacheService;
import com.juneyaoair.oneorder.api.common.CommonService;
import com.juneyaoair.oneorder.common.dto.BizDto;
import com.juneyaoair.oneorder.common.dto.RequestDataDto;
import com.juneyaoair.oneorder.common.dto.enums.ChannelCodeEnum;
import com.juneyaoair.oneorder.common.dto.enums.InterFlagEnum;
import com.juneyaoair.oneorder.common.enums.LanguageEnum;
import com.juneyaoair.oneorder.constant.SecurityConstants;
import com.juneyaoair.oneorder.core.context.SecurityContextHolder;
import com.juneyaoair.oneorder.flight.dto.FlightNotice;
import com.juneyaoair.oneorder.flight.dto.FlightReminder;
import com.juneyaoair.oneorder.mainpage.config.MainPageConfig;
import com.juneyaoair.oneorder.mainpage.dto.AirportInfoDto;
import com.juneyaoair.oneorder.mainpage.dto.homepage.*;
import com.juneyaoair.oneorder.mainpage.mapstruct.AirPortInfoDTOMapstruct;
import com.juneyaoair.oneorder.mainpage.service.IHomeService;
import com.juneyaoair.oneorder.mobile.config.RedisConstantConfig;
import com.juneyaoair.oneorder.mobile.utils.Redis0Util;
import com.juneyaoair.oneorder.restresult.enums.SuccessCode;
import com.juneyaoair.oneorder.tools.utils.DateUtil;
import com.juneyaoair.oneorder.tools.utils.HoAirGsonUtil;
import com.juneyaoair.oneorder.tools.utils.JacksonUtil;
import com.juneyaoair.oneorder.tools.utils.RedisKeyUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @date 2023/6/19 11:22
 */
@Slf4j
@Service
public class HomeServiceImpl extends CommonService implements IHomeService {
    @Autowired
    private FlightBasicProviderClient flightBasicProviderClient;
    @Autowired
    private FlightBasicConsumerClient flightBasicConsumerClient;
    @Autowired
    private CacheService cacheService;
    @Resource
    private Redis0Util redis0Util;
    @Autowired
    private IBasicService basicService;
    @Autowired
    private MainPageConfig mainPageConfig;

    @Override
    public List<VersionDto> queryDataVersion(RequestDataDto requestData) {
        VersionReq versionReq = VersionReq.builder()
                .channelCode(SecurityContextHolder.get(SecurityConstants.HEAD_CHANNEL_NO))
                .platformInfo("")
                .appVersion("")
                .build();
        BaseResponseDTO<List<VersionDto>> baseResponseDTO = flightBasicProviderClient.getDataVersionList(versionReq);
        if (WSEnum.SUCCESS.resultCode.equals(baseResponseDTO.getResultCode())) {
            List<VersionDto> versionDtoList = baseResponseDTO.getObjData();
            if (CollectionUtils.isNotEmpty(versionDtoList) && StringUtils.isNotBlank(requestData.getFfpNo())) {
                for (VersionDto ver : versionDtoList) {
                    String versionNo = ver.getVersionNo();
                    ver.setVersionNo(requestData.getFfpNo() + "_" + versionNo);
                }
            }
            return versionDtoList;
        }
        return new ArrayList<>();
    }

    @Override
    public List<FlightReminder> queryFlightNotice(RequestDataDto<FlightNotice> requestData) {
        List<FlightReminder> allFlightReminderList = new ArrayList<>();
        
        // 获取行程类型
        ApiCityInfoDto depCityInfo = cacheService.getLocalCity(requestData.getData().getDepCity());
        ApiCityInfoDto arrCityInfo = cacheService.getLocalCity(requestData.getData().getArrCity());
        String interFlag = InterFlagEnum.D.getCode();
        if (InterFlagEnum.I.getCode().equals(depCityInfo.getIsInternational()) || InterFlagEnum.I.getCode().equalsIgnoreCase(arrCityInfo.getIsInternational())) {
            interFlag = InterFlagEnum.I.getCode();
        }

        Date curDate = new Date();
        String finalInterFlag = interFlag;
        String channelCode = requestData.getChannelNo();

        // 获取当前语言
        LanguageEnum language = requestData.getLanguage();

        // 调用Provider获取出行提醒
        com.juneyaoair.flightbasic.commondto.ResponseData<List<TravelReminderResponse>> response = flightBasicProviderClient.getTravelReminder(new RequestData<>());
        if (response != null && response.getData() != null) {
            List<TravelReminderResponse> reminderList = response.getData();
            
            // 过滤并转换提醒
            List<FlightReminder> flightReminderList = reminderList.stream()
                .filter(reminder -> filterChannel(channelCode, reminder.getChannel()))
                .filter(reminder -> filterTrip(finalInterFlag, Collections.singletonList(reminder.getRouteType())))
                .filter(reminder -> filterRoute(requestData.getData().getDepCity(), requestData.getData().getArrCity(), 
                    reminder.getAvailableRoute(), reminder.getUnavailableRoute()))
                .filter(reminder -> filterTime(curDate, 
                    DateUtil.formatDate(reminder.getStartTime(), DateUtil.YYYY_MM_DD_HH_MM_SS_PATTERN),
                    DateUtil.formatDate(reminder.getEndTime(), DateUtil.YYYY_MM_DD_HH_MM_SS_PATTERN),
                    DateUtil.YYYY_MM_DD_HH_MM_SS_PATTERN))
                .sorted(Comparator.comparing((TravelReminderResponse reminder) -> Integer.parseInt(reminder.getDisplayType())).reversed())
                .map(reminder -> {
                    FlightReminder flightReminder = new FlightReminder();
                    // 根据当前语言获取对应内容
                    if (reminder.getContents() != null && !reminder.getContents().isEmpty()) {
                        // 查找匹配当前语言的内容
                        Optional<TravelReminderContentDTO> matchedContent = reminder.getContents().stream()
                            .filter(content -> language.name().equals(content.getLanguageTag()))
                            .findFirst();
                        
                        // 如果找不到当前语言的内容，返回null，这样会被过滤掉
                        if (!matchedContent.isPresent()) {
                            return null;
                        }
                        
                        TravelReminderContentDTO content = matchedContent.get();
                        flightReminder.setTitle(content.getTitle());
                        flightReminder.setReminderContent(content.getContent());
                    }
                    flightReminder.setUpdateTime(DateUtil.formatDate(reminder.getStartTime(), DateUtil.YYYY_MM_DD_HH_MM_SS_PATTERN));
                    return flightReminder;
                })
                .filter(Objects::nonNull)  // 过滤掉null值
                .collect(Collectors.toList());

            if (CollectionUtils.isNotEmpty(flightReminderList)) {
                allFlightReminderList.addAll(flightReminderList);
            }
        }

        // 如果是G_B2C渠道,不处理出发到达城市提醒
        if(ChannelCodeEnum.G_B2C.getChannelCode().equals(channelCode)) {
            return allFlightReminderList;
        }

        //出发城市提醒
        if (depCityInfo != null && CollectionUtils.isNotEmpty(depCityInfo.getCityWarnInfoList())) {
            for (CityWarnInfoDTO cityWarnInfoDTO : depCityInfo.getCityWarnInfoList()) {
                if (StringUtils.isNotBlank(cityWarnInfoDTO.getDepWarnContent()) && DateUtil.compareDate(curDate,cityWarnInfoDTO.getDepStartDate() + " 00:00:00", cityWarnInfoDTO.getDepEndDate() + " 23:59:59", DateUtil.YYYY_MM_DD_HH_MM_SS_PATTERN)
                        && StringUtils.isNotBlank(cityWarnInfoDTO.getDepRouteType()) && cityWarnInfoDTO.getDepRouteType().contains(interFlag)) {
                    FlightReminder flightReminder = new FlightReminder();
                    flightReminder.setTitle(cityWarnInfoDTO.getDepWarnTitle());
                    flightReminder.setReminderContent(cityWarnInfoDTO.getDepWarnContent());
                    flightReminder.setUpdateTime(StringUtils.isBlank(cityWarnInfoDTO.getUpdateTime()) ? cityWarnInfoDTO.getCreateTime() : cityWarnInfoDTO.getUpdateTime());
                    allFlightReminderList.add(flightReminder);
                }
            }
        }
        //到达城市提醒
        if (depCityInfo != null && CollectionUtils.isNotEmpty(arrCityInfo.getCityWarnInfoList())) {
            for (CityWarnInfoDTO cityWarnInfoDTO : arrCityInfo.getCityWarnInfoList()) {
                if (StringUtils.isNotBlank(cityWarnInfoDTO.getArrWarnContent()) && DateUtil.compareDate(curDate,cityWarnInfoDTO.getArrStartDate() + " 00:00:00", cityWarnInfoDTO.getArrEndDate() + " 23:59:59", DateUtil.YYYY_MM_DD_HH_MM_SS_PATTERN)
                        && StringUtils.isNotBlank(cityWarnInfoDTO.getArrRouteType()) && cityWarnInfoDTO.getArrRouteType().contains(interFlag)) {
                    FlightReminder flightReminder = new FlightReminder();
                    flightReminder.setTitle(cityWarnInfoDTO.getArrWarnTitle());
                    flightReminder.setReminderContent(cityWarnInfoDTO.getArrWarnContent());
                    flightReminder.setUpdateTime(StringUtils.isBlank(cityWarnInfoDTO.getUpdateTime()) ? cityWarnInfoDTO.getCreateTime() : cityWarnInfoDTO.getUpdateTime());
                    allFlightReminderList.add(flightReminder);
                }
            }
        }

        return allFlightReminderList;
    }

    /***
     * 生效时间过滤
     * @param startDate yyyy-MM-dd HH:mm
     * @param endDate  yyyy-MM-dd HH:mm
     * @return
     */
    private boolean filterTime(Date date, String startDate, String endDate,String dateFormat) {
        return DateUtil.compareDate(date,startDate, endDate , dateFormat);
    }

    /**
     * 渠道过滤
     * @param channelCode 当前渠道代码
     * @param channels 适用渠道列表(逗号分隔)
     * @return 是否匹配
     */
    private boolean filterChannel(String channelCode, String channels) {
        if (StringUtils.isBlank(channels)) {
            return true;
        }
        if (StringUtils.isBlank(channelCode)) {
            return false;
        }
        List<String> channelList = Arrays.asList(channels.split(","));
        return channelList.contains(channelCode);
    }

    /**
     * 航程类别过滤
     * @param interFlag 当前航线类型(D-国内、I-国际)
     * @param routeTypeList 适用航线类型(A-全部、D-国内、I-国际)
     * @return
     */
    private boolean filterTrip(String interFlag, List<String> routeTypeList) {
        if(CollectionUtils.isEmpty(routeTypeList)){
            return true;
        }
        String routeType = routeTypeList.get(0);
        // A-全部,直接返回true
        if("A".equals(routeType)) {
            return true;
        }
        // 其他情况需要匹配当前航线类型
        return routeType.equals(interFlag);
    }
    /**
     * 航程过滤
     * @param depCity 出发城市
     * @param arrCity 到达城市
     * @param availableRoute 适用航线列表
     * @param unavailableRoute 不适用航线列表
     * @return
     */
    private boolean filterRoute(String depCity, String arrCity, String availableRoute, String unavailableRoute) {
        if(StringUtils.isBlank(availableRoute) && StringUtils.isBlank(unavailableRoute)){
            return true;
        }
        
        // 先检查不可用航线
        if(StringUtils.isNotBlank(unavailableRoute)) {
            List<String> unavailableRouteList = Arrays.asList(unavailableRoute.split(","));
            // 检查是否在不可用航线中
            if(isRouteMatch(depCity, arrCity, unavailableRouteList)) {
                return false;
            }
        }
        
        // 再检查可用航线
        if(StringUtils.isNotBlank(availableRoute)) {
            List<String> availableRouteList = Arrays.asList(availableRoute.split(","));
            return isRouteMatch(depCity, arrCity, availableRouteList);
        }
        
        return true;
    }

    /**
     * 检查航线是否匹配
     * @param depCity 出发城市
     * @param arrCity 到达城市
     * @param routeList 航线列表
     * @return
     */
    private boolean isRouteMatch(String depCity, String arrCity, List<String> routeList) {
        if(CollectionUtils.isEmpty(routeList)) {
            return false;
        }
        
        // 检查特殊值
        if(routeList.contains("*") || routeList.contains("*-*")) {
            return true;
        }
        if(routeList.contains("Domestic")) {
            // TODO: 需要判断depCity和arrCity是否都是国内城市
            return true;
        }
        if(routeList.contains("INTL")) {
            // TODO: 需要判断depCity或arrCity是否有国际城市
            return true;
        }

        // 检查具体航线
        String route = depCity + "-" + arrCity;
        String wildcardDepRoute = depCity + "-*";
        String wildcardArrRoute = "*-" + arrCity;
        
        return routeList.contains(route) || 
               routeList.contains(wildcardDepRoute) || 
               routeList.contains(wildcardArrRoute);
    }

    @Override
    public String getPackageAd() {
        String key = RedisConstantConfig.TAOLXPACKAGE;
        Object obj = redis0Util.get(key);
        if (ObjectUtils.isEmpty(obj)) {
            return "";
        }
        return (String) obj;
    }

    @Override
    public List<RotationChart> queryRotationChart(RequestDataDto<RotationChartReq> data) {
        PictureRequestDTO requestDTO = new PictureRequestDTO();
        requestDTO.setPicLocation(data.getData().picLocation);
        requestDTO.setChannelCode(data.getChannelNo());
        requestDTO.setLanguage(data.getLanguage().name());
        requestDTO.setClientVersion(fetchClientVersion());
        BaseResponseDTO<List<PicDTO>> picResp = flightBasicConsumerClient.getPictureList(requestDTO);
        if (picResp != null && picResp.getObjData() != null) {
            return picResp.getObjData().stream().map(this::getRotationChart).collect(Collectors.toList());
        }
        return null;
    }

    @Override
    public PageInfo<AdvertisementDto> queryAdvertisementListByPage(BizDto bizDto, AdvertisementParam advertisementParam) {
        return basicService.queryAdvertisementListByPage(bizDto, advertisementParam);
    }

    @Override
    public List<AirportInfoDto> queryAllAirportInfo(BizDto bizDto) {
        BaseRequestDTO baseRequestDTO = createBaseRequestDTO(bizDto.getIp(),bizDto.getHeadChannelCode(),null);
        BaseResultDTO<List<AirPortInfoDTO>> baseResultDTO =  flightBasicConsumerClient.getAllAirPortInfoV2(baseRequestDTO);
        if(WSEnum.SUCCESS.resultCode.equals(baseResultDTO.getResultCode()) && CollectionUtils.isNotEmpty(baseResultDTO.getResult())){
            List<AirportInfoDto> airportInfoDtoList = AirPortInfoDTOMapstruct.mapper.toAirportInfoDtoList(baseResultDTO.getResult());
            airportInfoDtoList.sort(Comparator.comparing(AirportInfoDto::getCityPinYin));
            return airportInfoDtoList;
        }
        return new ArrayList<>();
    }

    @Override
    public GetWechatQRCodeBffResp getSuccessResponseData(RequestDataDto<GetWechatQRCodeBffReq> req) {
        GetWechatQRCodeBffReq data = req.getData();
        GetWechatQRCodeReq wxQRReq = new GetWechatQRCodeReq();

        wxQRReq.envVersion = mainPageConfig.getQueryWechatQRCodeVersion();
        wxQRReq.sourceType = req.getData().sourceType;
        String sceneMD5 = MD5.create().digestHex(data.scene);
        wxQRReq.scene = sceneMD5;
        String wxQRCodeKey = RedisKeyUtil.getWXQRCode(sceneMD5);
        Object info = redisUtil.get(wxQRCodeKey);
        if (info != null) {
            WechatQRCodeInfoPO wechatQRCodePO = JacksonUtil.objectToDto(info, new TypeReference<WechatQRCodeInfoPO>() {
            });
            if (wechatQRCodePO != null && StrUtil.isNotBlank(wechatQRCodePO.qrCodeStr) && StrUtil.isNotBlank(wechatQRCodePO.sceneOrig)) {
                GetWechatQRCodeBffResp bffResp = new GetWechatQRCodeBffResp();
                bffResp.timeRemaining = Long.parseLong(mainPageConfig.getQueryWechatQRCodeExpire());
                bffResp.qrCodeStr = wechatQRCodePO.qrCodeStr;
                redisUtil.expire(wxQRCodeKey, Long.parseLong(mainPageConfig.getQueryWechatQRCodeExpire()));
                return bffResp;
            }
        }
        com.juneyaoair.flightbasic.commondto.RequestData<GetWechatQRCodeReq> providerReq = new com.juneyaoair.flightbasic.commondto.RequestData<>();
        providerReq.setData(wxQRReq);
        providerReq.setOriginIp(req.getOriginIp());
        providerReq.setChannelNo(req.getChannelNo());
        providerReq.setFfpId(req.getFfpId());
        providerReq.setFfpNo(req.getFfpNo());
        com.juneyaoair.flightbasic.commondto.ResponseData<GetWechatQRCodeResp> wechatUnlimitedQRCode = flightBasicProviderClient.getWechatUnlimitedQRCode(providerReq);
        if (wechatUnlimitedQRCode == null || !SuccessCode.SUCCESS.name().equalsIgnoreCase(wechatUnlimitedQRCode.getCode())
                || wechatUnlimitedQRCode.getData() == null || StrUtil.isBlank(wechatUnlimitedQRCode.getData().qrCodeStr)) {
            log.error("feign invoke fail:" + Optional.ofNullable(wechatUnlimitedQRCode)
                    .map(com.juneyaoair.flightbasic.commondto.ResponseData::getData).map(i -> i.errmsg)
                    .orElse(Optional.ofNullable(wechatUnlimitedQRCode)
                            .map(com.juneyaoair.flightbasic.commondto.ResponseData::getMessage)
                            .orElse(null)));
            ServiceException.fail(CommonErrorCode.BUSINESS_ERROR.getMessage());
        }
        GetWechatQRCodeBffResp bffResp = new GetWechatQRCodeBffResp();
        bffResp.qrCodeStr = wechatUnlimitedQRCode.getData().qrCodeStr;
        bffResp.timeRemaining = Long.parseLong(mainPageConfig.getQueryWechatQRCodeExpire());

        WechatQRCodeInfoPO codeInfoPO = new WechatQRCodeInfoPO();
        codeInfoPO.qrCodeStr = wechatUnlimitedQRCode.getData().qrCodeStr;
        codeInfoPO.sceneOrig = data.scene;
        redisUtil.set(wxQRCodeKey, codeInfoPO, Long.parseLong(mainPageConfig.getQueryWechatQRCodeExpire()));
        return bffResp;
    }


    /**
     * 返回 联名信用卡广告信息 - 官网配置
     *
     * @return
     */
    @Override
    public List<CoBrandCreditCardAdBO> getCreditCardAd(BizDto bizDto) {
        BaseRequestDTO baseRequestDTO = createBaseRequestDTO(bizDto.getIp(), bizDto.getHeadChannelCode(), null);
        BaseResultDTO<List<CoBrandCreditCardAdBO>> resultDTO = flightBasicProviderClient.getCoBrandCreditCardAd(baseRequestDTO);
        // filter 是否发布
        // filter 开始结束时间
        List<CoBrandCreditCardAdBO> dto = resultDTO.getResult().stream()
                .filter(CoBrandCreditCardAdBO::isThemeEnable)
                .filter(s -> cn.hutool.core.date.DateUtil.isIn(new Date(), cn.hutool.core.date.DateUtil.parse(s.getStartTime()), cn.hutool.core.date.DateUtil.parse(s.getEndTime())))
                .collect(Collectors.toList());

        // filter frontEndType == 2 官网
        dto.forEach(v -> {
            if (CollectionUtils.isNotEmpty(v.getDetail())) {
                v.setDetail(v.getDetail().stream().filter(s -> s.getFrontEndType() == 2).collect(Collectors.toList()));
            }
        });


        // dto sort by sort
        dto.sort(Comparator.comparingInt(CoBrandCreditCardAdBO::getSort));

        return dto;
    }

    @NotNull
    private RotationChart getRotationChart(PicDTO i) {
        RotationChart chart = new RotationChart();
        chart.setPicId(i.getPicId());
        chart.setStartTime(i.getStartTime());
        if (StringUtils.isNotBlank(chart.getStartTime())) {
            Date startTime = DateUtil.toDate(chart.getStartTime(), DateUtil.YYYY_MM_DD_HH_MM_PATTERN, DateUtil.YYYY_MM_DD_PATTERN);
            if (null != startTime) {
                chart.setStartTimeStamp(startTime.getTime());
            }
        }
        chart.setEndTime(i.getEndTime());
        if (StringUtils.isNotBlank(chart.getEndTime())) {
            Date endTime = DateUtil.toDate(chart.getEndTime(), DateUtil.YYYY_MM_DD_HH_MM_PATTERN, DateUtil.YYYY_MM_DD_PATTERN);
            if (null != endTime) {
                chart.setEndTimeStamp(endTime.getTime());
            }
        }
        chart.setPicLocation(i.getPicLocation());
        chart.setPicUrl(i.getPicUrl());
        chart.setPicInfoList(i.getPicInfoList());
        chart.setTitle(i.getTitle());
        chart.setLittleTitle(i.getLittleTitle());
        chart.setWinName(i.getWinName());
        chart.setDescription(i.getDescription());
        chart.setDescriptionPlainTxt(i.getDescriptionPlainTxt());
        chart.setUrl(i.getUrl());
        chart.setChannelCode(i.getChannelCode());
        chart.setIsLogin(i.getIsLogin());
        chart.setModeType(i.getModeType());
        chart.setIsShared(i.getIsShared());
        chart.setShareIconUrl(i.getShareIconUrl());
        chart.setVideoUrl(i.getVideoUrl());
        chart.setShareDesc(i.getShareDesc());
        chart.setIsGiftPoints(i.getIsGiftPoints());
        chart.setIsGiftCoupons(i.getIsGiftCoupons());
        chart.setStatisticalEvents(i.getStatisticalEvents());
        chart.setType(i.getType());
        chart.setShowLabelInfo(i.getShowLabelInfo());
        chart.setCabins(i.getCabins());
        chart.setPicShowTimes(i.getPicShowTimes());
        chart.setVideoOrimgShowTimes(i.getVideoOrimgShowTimes());
        chart.setAdvShow(i.getAdvShow());
        chart.setAirlines(i.getAirlines());
        chart.setEventType(i.getEventType());
        chart.setPicType(i.getPicType());
        chart.setEnabledMode(i.getEnabledMode());
        return chart;
    }

    @Override
    public MallProductResponse queryMallProducts(MallProductRequest request) {
        log.debug("查询积分商城商品，请求参数：{}", HoAirGsonUtil.objectToJson(request));

        MallProductResponse response = new MallProductResponse();

        try {
            ResponseData<List<CommonRecommendTravel>> mallProductResponse = flightBasicProviderClient.queryHotelProductsV2();
            // CommonRecommendTravel to MallProduct
            // List<MallProduct>
            // todo
            response.setList(mockProducts);

            log.debug("积分商城商品查询成功，返回商品数量：{}", response.getList().size());
        } catch (Exception e) {
            log.error("查询积分商城商品异常", e);
            response.setList(new ArrayList<>());
        }

        return response;
    }

}
