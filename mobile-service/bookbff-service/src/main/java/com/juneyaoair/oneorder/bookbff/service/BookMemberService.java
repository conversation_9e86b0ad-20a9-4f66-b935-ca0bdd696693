package com.juneyaoair.oneorder.bookbff.service;

import com.juneyaoair.flightbasic.crm.response.BillInfos;
import com.juneyaoair.flightbasic.request.crm.PtMemberDetailRequest;
import com.juneyaoair.flightbasic.response.crm.PtMemberDetail;

import java.util.List;

public interface BookMemberService {

    /**
     * 会员详情查询
     * @param ffpCardNo
     * @param requestItems
     * @return
     */
    PtMemberDetail getMemberDetail(String ffpCardNo, String [] requestItems);


    /**
     * 会员详情查询
     * @param memberDetailRequest
     * @return
     */
    PtMemberDetail getMemberDetail(PtMemberDetailRequest memberDetailRequest);


    /**
     * 判断是否存在航空累积积分
     * @param clientIp
     * @param ffpCardNo
     * @param requestItems
     * @return
     */
    boolean existMemberClubMiles(String clientIp, String ffpCardNo, List<String> requestItems);


    /**
     * 积分账单查询
     * @param clientIp
     * @param ffpCardNo
     * @param requestItems
     * @return
     */
    BillInfos mileageAccountQuery(String clientIp, String ffpCardNo, List<String> requestItems);
}
