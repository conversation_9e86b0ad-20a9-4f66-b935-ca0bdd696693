package com.juneyaoair.oneorder.bookbff.dto.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @ClassName QueryApplyRecordResp
 * @Description
 * <AUTHOR>
 * @Date 2024/6/4 10:10
 * @Version 1.0
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
public class QueryApplyRecordResp {

    private String pageIndex;

    private String pageSize;

    @ApiModelProperty(value = "办理记录列表")
    private List<ApplyRecordInfo> applyRecordList;
}
